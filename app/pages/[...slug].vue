<script lang="ts" setup>
const config = useConfig()
const { page } = useContent()

useSeoMeta({
  title: page?.value?.title || config.value.site?.title,
  titleTemplate: config.value.site?.titleTemplate,
  description: page?.value?.description,
})

useHead({
  htmlAttrs: {
    lang: config.value.site?.lang,
  },
})
</script>

<template>
  <main>
    <ContentRenderer v-if="page?.body" :value="page" />
  </main>
</template>
