<script lang="ts" setup>
defineProps<{
  links?: TextLink[]
}>()
</script>

<template>
  <ul class="flex-(~ y-center) gap-4">
    <li v-for="link in links" :key="link.to">
      <NuxtLink :to="link.to" :target="link.target" :rel="link.rel" class="flex-(inline y-center) gap-x-2 hover:text-accent-9">
        {{ link.label }}
        <UiIcon v-if="link.rel === 'noopener noreferrer'" name="external" class="size-3.5 text-accent-8" />
      </NuxtLink>
    </li>
  </ul>
</template>
