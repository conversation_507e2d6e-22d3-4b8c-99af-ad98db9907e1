<script lang="ts" setup>
const header = useConfig('header')
</script>

<template>
  <header class="sticky top-0 z-10">
    <div class="flex-(~ y-center) gap-x-6 justify-end px-6 py-4">
      <NuxtLink to="/" :title="header?.name">
        <h1 class="flex-(~ y-center) gap-2 c-accent-9 select-none">
          <AppHeaderLogo :logo="header?.logo" />

          <strong v-if="header?.name" class="text-lg font-bold tracking-tighter">
            {{ header?.name }}
          </strong>
        </h1>
      </NuxtLink>

      <AppHeaderNav :nav="header?.nav" class="flex-1" />
      <AppHeaderSocials :socials="socials" :github="github" />
    </div>
  </header>
</template>
