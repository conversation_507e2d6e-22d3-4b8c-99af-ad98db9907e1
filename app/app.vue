<script lang="ts" setup>
const route = useRoute()

const { data: page } = await useAsyncData(route.path, () => {
  return queryCollection('docs').path(route.path).first()
})

const { data: navigation } = await useAsyncData('navigation', () => {
  return queryCollectionNavigation('docs')
})

provide('page', page)
provide('navigation', navigation)
</script>

<template>
  <Html data-theme="default">
    <NuxtLoadingIndicator color="var(--ui-accent)" />
    <AppHeader />
    <NuxtPage />
    <AppFooter v-if="page?.footer?.enable !== false" />
  </Html>
</template>

<style>
body {
  background-color: var(--ui-bg);
}
</style>
