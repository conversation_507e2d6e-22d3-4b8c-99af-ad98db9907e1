import { fileURLToPath } from 'node:url'

export default defineNuxtConfig({
  modules: [
    '@weme-ui/nuxt',
    '@unocss/nuxt',
    '@nuxt/content',
  ],

  components: [
    {
      path: fileURLToPath(new URL('./app/components', import.meta.url)),
      extensions: ['vue', 'tsx'],
    },
    '~/components',
  ],

  vite: {
    optimizeDeps: {
      include: [
        '@vue/devtools-core',
        '@vue/devtools-kit',
        'reka-ui',
        'clsx',
        'tailwind-merge',
        'tailwind-variants',
      ],
    },
  },
})
