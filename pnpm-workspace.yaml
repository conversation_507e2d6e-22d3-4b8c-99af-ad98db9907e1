overrides:
  '@weme-ui/colors': link:../weme-ui/packages/colors
  '@weme-ui/nuxt': link:../weme-ui/packages/nuxt
  '@weme-ui/unocss-preset': link:../weme-ui/packages/unocss-preset

catalog:
  '@antfu/eslint-config': ^5.2.1
  '@commitlint/cli': ^19.8.1
  '@commitlint/config-conventional': ^19.8.1
  '@nuxt/content': ^3.6.3
  '@nuxt/icon': ^2.0.0
  '@unocss/nuxt': ^66.4.2
  '@vueuse/core': ^13.7.0
  '@vueuse/nuxt': ^13.7.0
  better-sqlite3: ^12.2.0
  bumpp: ^10.2.3
  clsx: ^2.1.1
  commitizen: ^4.3.1
  cz-git: ^1.12.0
  czg: ^1.12.0
  eslint: ^9.33.0
  eslint-plugin-pnpm: ^1.1.1
  jsonc-eslint-parser: ^2.4.0
  lint-staged: ^16.1.5
  nuxt: ^4.0.3
  reka-ui: ^2.4.1
  simple-git-hooks: ^2.13.1
  tailwind-merge: ^3.3.1
  tailwind-variants: ^2.1.0
  taze: ^19.1.0
  typescript: ^5.9.2
  ufo: ^1.6.1
  unocss: ^66.4.2
  vue: ^3.5.18
  vue-tsc: ^3.0.5
  yaml-eslint-parser: ^1.3.0

onlyBuiltDependencies:
  - '@parcel/watcher'
  - better-sqlite3
  - esbuild
  - simple-git-hooks
  - vue-demi
