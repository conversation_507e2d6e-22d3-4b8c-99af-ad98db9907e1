<script lang="ts" setup>
const props = defineProps<{
  socials?: IconLink[]
  github?: {
    repo: string
    branch?: string
    directory?: string
  }
}>()

const repoUrl = computed(() => {
  const href = `https://github.com/${props.github?.repo}`

  if (props.github?.branch)
    return `${href}/tree/${props.github?.branch}`

  if (props.github?.directory)
    return `${href}/${props.github?.directory}`

  return href
})
</script>

<template>
  <ul class="flex-(~ y-center) gap-4 select-none">
    <li v-for="social in socials" :key="social.to">
      <NuxtLink :to="social.to" :target="social.target" :rel="social.rel" class="text-accent-9 hover:text-accent-12">
        <UiIcon :name="social.icon" class="size-5" />
      </NuxtLink>
    </li>

    <li v-if="github">
      <NuxtLink
        :to="repoUrl"
        target="_blank"
        rel="noopener noreferrer"
        class="text-accent-9 hover:text-accent-12"
      >
        <UiIcon name="simple-icons:github" class="size-5" />
      </NuxtLink>
    </li>
  </ul>
</template>
