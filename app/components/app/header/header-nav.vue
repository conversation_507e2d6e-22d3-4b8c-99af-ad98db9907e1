<script lang="ts" setup>
import {
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuRoot,
  NavigationMenuTrigger,
  NavigationMenuViewport,
} from 'reka-ui'
import { cn } from '~/utils/styles'

defineProps<{
  nav?: NavItem[]
}>()
</script>

<template>
  <NavigationMenuRoot class="relative z-1 w-full">
    <NavigationMenuList class="flex gap-x-2">
      <NavigationMenuItem v-for="item in nav" :key="item.label" class="select-none">
        <template v-if="item.children && item.children.length">
          <NavigationMenuTrigger
            :class="cn(
              'group flex-(~ y-center) text-3.5 font-medium pl-4 pr-3 py-1.5 gap-2 leading-5 transition-colors tracking-tight c-accent-9 rounded-full',
              'hover:(c-accent-1 bg-accent-9) state-open:(c-accent-1 bg-accent-9)',
            )"
          >
            <span>{{ item.label }}</span>

            <UiIcon name="down" class="size-3.5 c-accent-9 transition group-data-[state=open]:(-rotate-180 c-accent-12)" />
          </NavigationMenuTrigger>

          <NavigationMenuContent
            :class="cn(
              'abs top-0 left-0 w-auto will-change-transform',
              'data-[motion=from-start]:animate-(slide-in-left duration-350 ease-in-out)',
              'data-[motion=from-end]:animate-(slide-in-right duration-350 ease-in-out)',
              'data-[motion=to-start]:animate-(slide-out-left duration-250 ease-in-out)',
              'data-[motion=to-end]:animate-(slide-out-right duration-250 ease-in-out)',
            )"
          >
            <NavigationMenuList class="isolate grid-(~ cols-2) gap-2 w-170 p-5 pb-5.5">
              <NavigationMenuItem v-for="child in item.children" :key="child.to">
                <NavigationMenuLink
                  class="flex-(~ y-center) items-start gap-x-4 px-3 py-2.5 rounded-lg hover:(bg-accent-1 ring ring-default)"
                  :href="child.to"
                  :rel="child.rel"
                >
                  <UiIconBox v-if="child.icon" :name="child.icon" size="lg" variant="outline" radius="md" />

                  <div class="flex-(~ 1 col) gap-y-1">
                    <strong class="flex-(~ y-center) gap-1 font-medium text-accent-12 leading-none">
                      {{ child.label }}
                      <UiIcon v-if="child.target === '_blank'" name="external" class="size-3.5 op-45" />
                    </strong>
                    <p v-if="child.description" class="text-(sm toned)">
                      {{ child.description }}
                    </p>
                  </div>
                </NavigationMenuLink>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenuContent>
        </template>

        <template v-else>
          <NavigationMenuLink
            class="group flex-(~ y-center) text-3.5 font-medium ring-default px-4 py-1.5 gap-2 leading-5 transition-colors c-accent-9 tracking-tight rounded-full hover:hover:(c-accent-1 bg-accent-9)"
            :href="item.to"
            :rel="item.rel"
            :target="item.target"
          >
            <UiIcon
              v-if="item.icon"
              :name="item.icon"
              class="size-3.5 c-accent-9 transition-colors group-hover:c-accent-1"
            />

            <span>{{ item.label }}</span>

            <UiIcon
              v-if="item.rel === 'noopener noreferrer'"
              name="external"
              class="size-3.5 c-accent-8"
            />
          </NavigationMenuLink>
        </template>
      </NavigationMenuItem>
    </NavigationMenuList>

    <div class="abs-(~ x-center) top-full w-auto">
      <NavigationMenuViewport
        :class="cn(
          'relative overflow-hidden mt-4 bg-white-4 backdrop-blur-xl rounded-lg b-(~ default) shadow-lg',
          'h-$reka-navigation-menu-viewport-height w-$reka-navigation-menu-viewport-width transition-[width,height]',
          'state-open:animate-(fade-in-up duration-200 ease-in-out)',
          'state-closed:animate-(fade-out-down duration-150 ease-in-out)',
        )"
      />
    </div>
  </NavigationMenuRoot>
</template>
