interface Linkable {
  to: string
  icon?: string
  rel?: string
  target?: '_blank' | '_self'
}

type TextLink = Linkable & {
  label: string
  description?: string
}

type IconLink = Linkable & {
  icon: string
  alt?: string
}

type NavItem = Omit<TextLink, 'to'> & {
  to?: string
  children?: TextLink[]
}

interface DocafeConfig {
  site?: {
    title?: string
    titleTemplate?: string
    description?: string
    lang?: string
  }

  header?: {
    logo?: string
    name?: string
    nav?: NavItem[]
  }

  footer?: {
    copyright?: string
    links?: TextLink[]
  }

  outline?: {
    title?: string
    extends?: {
      title?: string
      links?: TextLink[]
    }
  }

  socials?: IconLink[]

  github?: {
    repo: string
    branch?: string
    directory?: string
  }
}
