import { createSharedComposable } from '@vueuse/core'
import { defu } from 'defu'

const defaultConfig: DocafeConfig = {
  site: {
    title: 'Write beautiful docs built with Weme UI',
    titleTemplate: '%s - Docafe',
    description: 'Write beautiful docs built with Weme UI.',
    lang: 'en-US',
  },

  footer: {
    copyright: 'Published under the MIT License.',
  },

  outline: {
    title: 'On this page',
    extends: {
      title: 'Community',
      links: [
        {
          icon: 'lucide:star',
          label: 'Star on GitHub',
          to: 'https://github.com/weme-ui/docafe',
          rel: 'noopener noreferrer',
          target: '_blank',
        },
        {
          icon: 'lucide:bug',
          label: 'Report an issue',
          to: 'https://github.com/weme-ui/docafe/issues',
          rel: 'noopener noreferrer',
          target: '_blank',
        },
      ],
    },
  },

  github: {
    repo: 'weme-ui/docafe',
  },
}

function _useConfig<K extends keyof DocafeConfig>(key: K): ComputedRef<DocafeConfig[K]> {
  const appConfig = computed<DocafeConfig>(() => useAppConfig()?.docafe || {})
  const resolved = defu(appConfig.value, defaultConfig)

  const { page } = useContent()

  const config = {
    ...resolved,

    header: {
      ...resolved.header,
      nav: page?.value?.nav as NavItem[] || resolved.header?.nav || [],
    },
  }

  return computed(() => {
    return config[key]
  })
}

export const useConfig = /* @__PURE__ */ createSharedComposable(_useConfig)
