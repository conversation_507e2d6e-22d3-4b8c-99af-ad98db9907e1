{"name": "docafe", "type": "module", "version": "0.0.0", "packageManager": "pnpm@10.14.0", "description": "Write beautiful docs built with Weme UI.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/weme-ui/docafe", "repository": {"type": "git", "url": "git+https://github.com/weme-ui/docafe.git", "directory": "docs"}, "bugs": {"url": "https://github.com/weme-ui/docafe/issues"}, "keywords": ["weme-ui", "weme-ui-docafe"], "main": "./nuxt.config.ts", "scripts": {"dev": "nuxi dev docs", "dev:prepare": "nuxt prepare . && nuxt prepare docs", "build": "nuxt build docs", "generate": "nuxt generate docs", "preview": "nuxt preview docs", "lint": "eslint .", "typecheck": "vue-tsc --noEmit", "taze": "taze major -I -r", "commit": "czg"}, "dependencies": {"@nuxt/content": "catalog:", "@weme-ui/colors": "link:../weme-ui/packages/colors", "@weme-ui/nuxt": "link:../weme-ui/packages/nuxt", "@weme-ui/unocss-preset": "link:../weme-ui/packages/unocss-preset", "clsx": "catalog:", "reka-ui": "catalog:", "tailwind-merge": "catalog:", "tailwind-variants": "catalog:", "ufo": "catalog:"}, "devDependencies": {"@antfu/eslint-config": "catalog:", "@commitlint/cli": "catalog:", "@commitlint/config-conventional": "catalog:", "@nuxt/icon": "catalog:", "@unocss/nuxt": "catalog:", "@vueuse/core": "catalog:", "@vueuse/nuxt": "catalog:", "better-sqlite3": "catalog:", "bumpp": "catalog:", "commitizen": "catalog:", "cz-git": "catalog:", "czg": "catalog:", "eslint": "catalog:", "eslint-plugin-pnpm": "catalog:", "jsonc-eslint-parser": "catalog:", "lint-staged": "catalog:", "nuxt": "catalog:", "simple-git-hooks": "catalog:", "taze": "catalog:", "typescript": "catalog:", "unocss": "catalog:", "vue": "catalog:", "vue-tsc": "catalog:", "yaml-eslint-parser": "catalog:"}, "simple-git-hooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{vue,ts,tsx,mjs,cjs}": "eslint --fix"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}